package org.moderncampus.integration.ellucian.workflow.transform.ethos

import org.apache.camel.Exchange
import org.moderncampus.integration.dto.base.IPaginationConstruct
import org.moderncampus.integration.dto.base.SearchEntityRequest
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants

import static org.moderncampus.integration.ellucian.component.endpoint.config.EllucianCloudEndpointConfiguration.QUERY_PARAMS
import static org.moderncampus.integration.route.support.RouteSupport.routeQueryParamStr

abstract class BaseEthosSearchTransforms {

    private Map<String, String> buildPaginationParameters(IPaginationConstruct paginationConstruct) {
        Map<String, String> paginationParams = [:] as Map<String, String>
        if (paginationConstruct?.pageSize) {
            paginationParams[EllucianConstants.ETHOS_PAGINATION_LIMIT] = paginationConstruct.pageSize as String
        }
        if (paginationConstruct?.pageOffset) {
            paginationParams[EllucianConstants.ETHOS_PAGINATION_OFFSET] = paginationConstruct.pageOffset as String
        }
        return paginationParams
    }

    void buildSearchCriteria(Exchange exchange) {
        SearchEntityRequest entityRequest = exchange.message.getBody(SearchEntityRequest.class)
        if (entityRequest != null) {
            Map<String, String> queryParams = buildPaginationParameters(entityRequest.paginationConstruct)
            buildAdditionalCriteria(exchange, queryParams, entityRequest.criteria)
            if (queryParams) {
                exchange.message.setHeader(QUERY_PARAMS, routeQueryParamStr(queryParams))
            }
        }
    }

    abstract void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria)
}
