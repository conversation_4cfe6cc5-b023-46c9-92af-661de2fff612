package org.moderncampus.integration.dto.cewd;

import org.moderncampus.integration.dto.base.BaseDTO;
import org.moderncampus.integration.validation.IValidationGroups;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CewdAddress extends BaseDTO {

    @NotNull(message = "Address Type must be defined", groups = IValidationGroups.IIntPersonUpdate.class)
    String type;

    @NotNull(message = "Line 1 of address must be defined", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String line1;

    String line2;

    @NotNull(message = "City must be defined", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String city;

    @NotNull(message = "State must be defined", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String state;

    @NotNull(message = "Postal Code must be defined", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String postalCode;

    @NotNull(message = "Country must be defined", groups = IValidationGroups.IWorkdayDuplicateCheckRequest.class)
    String country;
}
