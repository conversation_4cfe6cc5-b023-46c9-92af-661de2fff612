package org.moderncampus.integration.dto.core;

import java.time.LocalDate;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCAddress extends BaseDTO {

    String addressLine1;
    String addressLine2;
    String addressLine3;
    String addressLine4;
    String city;
    MCState state;
    MCCounty county;
    MCCountry country;
    String postalCode;

    Boolean preferred;
    LocalDate effStart;
    LocalDate effEnd;

    MCCategoryType type;
}
