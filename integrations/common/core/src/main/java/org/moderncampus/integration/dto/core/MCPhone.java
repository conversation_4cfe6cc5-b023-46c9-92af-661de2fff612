package org.moderncampus.integration.dto.core;

import org.moderncampus.integration.dto.base.BaseDTO;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCPhone extends BaseDTO {

    MCCategoryType type;

    Boolean preferred;

    String countryCode;

    String number;

    String extension;

}