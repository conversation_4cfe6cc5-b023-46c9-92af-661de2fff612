package org.moderncampus.integration.dto.core;

import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCCombinedInstructor extends BaseDTO {

    @Override
    @Schema(description = "The globally unique identifier for an instructor to be used in all external references.")
    public String getId() {
        return super.getId();
    }

    List<MCCombinedInstructorName> names;
    List<MCCombinedInstructorEmail> emails;
    List<MCCombinedInstructorType> instructorTypes;
    List<MCCombinedInstructorDepartment> instructorDepartments;
    String status;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorEmail extends BaseDTO {

        Boolean preferred;
        MCCombinedInstructorEmailType type;
        String address;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorEmailType extends BaseDTO {

        String id;
        String name;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorName extends BaseDTO {

        @Schema(description = "Indicates the preferred name for the person. Only one name should be set to preferred for a person.")
        Boolean preferred;
        MCCombinedInstructorNameType type;
        String title;
        String firstName;
        String middleName;
        String lastName;
        String suffix;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorNameType extends BaseDTO {

        String id;
        String name;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorType extends BaseDTO {

        String id;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCCombinedInstructorDepartment extends BaseDTO {

        String id;
    }
}
