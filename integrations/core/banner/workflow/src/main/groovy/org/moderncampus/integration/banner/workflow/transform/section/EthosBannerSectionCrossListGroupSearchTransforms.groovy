package org.moderncampus.integration.banner.workflow.transform.section


import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.banner.workflow.transform.common.search.BaseEthosBannerSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosBannerSectionCrossListGroupSearchTransforms extends BaseEthosBannerSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        String termCode = searchCriteria["termCode"]
        if (termCode) {
            queryParams["termCode"] = termCode
        }

        String xlstGroup = searchCriteria["xlstGroup"]
        if (xlstGroup) {
            queryParams["xlstGroup"] = xlstGroup
        }

        def maxEnrl = searchCriteria["maxEnrl"]
        if (maxEnrl) {
            queryParams["maxEnrl"] = maxEnrl as String
        }
    }

}
