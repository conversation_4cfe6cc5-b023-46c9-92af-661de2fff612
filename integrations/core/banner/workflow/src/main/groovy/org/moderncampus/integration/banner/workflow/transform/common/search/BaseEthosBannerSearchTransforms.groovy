package org.moderncampus.integration.banner.workflow.transform.common.search


import org.apache.camel.Exchange
import org.moderncampus.integration.dto.base.IPaginationConstruct
import org.moderncampus.integration.dto.base.SearchEntityRequest

import static org.moderncampus.integration.banner.component.constants.Constants.ETHOS_BANNER_PAGINATION_LIMIT
import static org.moderncampus.integration.banner.component.constants.Constants.ETHOS_BANNER_PAGINATION_OFFSET
import static org.moderncampus.integration.banner.component.endpoint.config.BannerEndpointConfiguration.QUERY_PARAMS
import static org.moderncampus.integration.route.support.RouteSupport.routeQueryParamStr

abstract class BaseEthosBannerSearchTransforms {

    private Map<String, String> buildPaginationParameters(IPaginationConstruct paginationConstruct) {
        Map<String, String> paginationParams = [:] as Map<String, String>
        if (paginationConstruct?.pageSize) {
            paginationParams[ETHOS_BANNER_PAGINATION_LIMIT] = paginationConstruct.pageSize as String
        }
        if (paginationConstruct?.pageOffset) {
            paginationParams[ETHOS_BANNER_PAGINATION_OFFSET] = paginationConstruct.pageOffset as String
        }
        return paginationParams
    }

    void buildSearchCriteria(Exchange exchange) {
        SearchEntityRequest entityRequest = exchange.message.getBody(SearchEntityRequest.class)
        if (entityRequest != null) {
            Map<String, String> queryParams = buildPaginationParameters(entityRequest.paginationConstruct)
            buildAdditionalCriteria(exchange, queryParams, entityRequest.criteria)
            if (queryParams) {
                exchange.message.setHeader(QUERY_PARAMS, routeQueryParamStr(queryParams))
            }
        }
    }

    abstract void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria)
}
