package org.moderncampus.integration.webservice.core.controller;

import static org.moderncampus.integration.Constants.BASE_API_URI;
import static org.moderncampus.integration.Constants.VERSION_1;

import java.util.List;

import org.moderncampus.integration.dto.base.IntegrationRequest;
import org.moderncampus.integration.dto.base.IntegrationResponse;
import org.moderncampus.integration.dto.core.MCAcademicLevel;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCHealthCheck;
import org.moderncampus.integration.dto.core.MCInstructionalMethod;
import org.moderncampus.integration.dto.core.MCCombinedInstructor;
import org.moderncampus.integration.dto.core.MCInstructor;
import org.moderncampus.integration.dto.core.MCLocation;
import org.moderncampus.integration.dto.core.MCOrganizationalUnit;
import org.moderncampus.integration.dto.core.MCRoom;
import org.moderncampus.integration.dto.core.MCSubject;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCFinalGradeRequest;
import org.moderncampus.integration.webservice.core.dto.request.CreateMCOrganizationRequest;
import org.moderncampus.integration.webservice.core.dto.request.SearchMCAcademicPeriodRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCFinalGradeRequest;
import org.moderncampus.integration.webservice.core.dto.request.UpdateMCOrganizationRequest;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCFinalGradeResponse;
import org.moderncampus.integration.webservice.core.dto.response.CreateMCOrganizationResponse;
import org.moderncampus.integration.webservice.core.service.CoreIntegrationService;
import org.moderncampus.integration.webservice.dto.APIErrorResponseWrapper;
import org.moderncampus.integration.webservice.dto.BasePaginationRequest;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.node.ObjectNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Validated
@RestController
@RequestMapping(BASE_API_URI + VERSION_1)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityRequirement(name = "bearerAuth")
public class CoreIntegrationController {

    static final String BY_ID = "/{id}";
    static final String ORGANIZATIONAL_UNITS = "organizational-units";
    static final String SUBJECTS = "subjects";
    static final String INSTRUCTIONAL_METHODS = "instructional-methods";
    static final String LOCATIONS = "locations";
    static final String ACADEMIC_LEVELS = "academic-levels";
    static final String ROOMS = "rooms";
    static final String GET_ORGANIZATIONAL_UNIT = ORGANIZATIONAL_UNITS + "/" + "{id}";
    static final String GET_SUBJECT = SUBJECTS + "/" + "{id}";
    static final String GET_INSTRUCTIONAL_METHOD = INSTRUCTIONAL_METHODS + "/" + "{id}";
    static final String GET_LOCATION = LOCATIONS + "/" + "{id}";
    static final String GET_ACADEMIC_LEVEL = ACADEMIC_LEVELS + "/" + "{id}";
    static final String GET_ROOM = ROOMS + "/" + "{id}";
    static final String ACADEMIC_PERIODS = "academic-periods";
    static final String GET_ACADEMIC_PERIODS = "academic-periods" + "/" + "{id}";
    static final String INSTRUCTORS = "instructors";
    static final String GET_INSTRUCTOR = INSTRUCTORS + "/" + "{id}";
    static final String GET_ADDRESSES = "addresses";
    static final String GET_ADDRESS = GET_ADDRESSES + "/" + "{id}";
    static final String HEALTH_CHECK = "health-check";
    static final String FINAL_GRADES = "final-grades";
    static final String PUT_FINAL_GRADE = FINAL_GRADES + "/" + "{id}";
    static final String ORGANIZATIONS = "organizations";
    static final String UPDATE_ORGANIZATION = ORGANIZATIONS + "/" + "{id}";

    protected CoreIntegrationService service;

    @Operation(summary = "Get all Organizational Units From External System", tags = {"Organizational Unit"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = ORGANIZATIONAL_UNITS)
    public IntegrationResponse<List<MCOrganizationalUnit>> getOrganizationalUnits() throws Exception {
        return service.getOrganizationalUnits();
    }

    @Operation(summary = "Get Organizational Unit From External System", tags = {"Organizational Unit"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_ORGANIZATIONAL_UNIT)
    public IntegrationResponse<MCOrganizationalUnit> getOrganizationalUnit(@PathVariable String id) throws Exception {
        return service.getOrganizationalUnit(id);
    }

    @Operation(summary = "Get all Subjects From External System", tags = {"Subject"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = SUBJECTS)
    public IntegrationResponse<List<MCSubject>> getSubjects() throws Exception {
        return service.getSubjects();
    }

    @Operation(summary = "Get Subject From External System", tags = {"Subject"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_SUBJECT)
    public IntegrationResponse<MCSubject> getSubject(@PathVariable String id) throws Exception {
        return service.getSubject(id);
    }

    @Operation(summary = "Get all Instructional Methods From External System", tags = {"Instructional Method"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = INSTRUCTIONAL_METHODS)
    public IntegrationResponse<List<MCInstructionalMethod>> getInstructionalMethods(
            @ParameterObject BasePaginationRequest paginationRequest) throws Exception {
        return service.getInstructionalMethods(paginationRequest);
    }

    @Operation(summary = "Get Instructional Method From External System", tags = {"Instructional Method"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_INSTRUCTIONAL_METHOD)
    public IntegrationResponse<MCInstructionalMethod> getInstructionalMethod(@PathVariable String id) throws Exception {
        return service.getInstructionalMethod(id);
    }

    @Operation(summary = "Get all Locations From External System", tags = {"Location"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = LOCATIONS)
    public IntegrationResponse<List<MCLocation>> getLocations() throws Exception {
        return service.getLocations();
    }

    @Operation(summary = "Get Location From External System", tags = {"Location"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_LOCATION)
    public IntegrationResponse<MCLocation> getLocation(@PathVariable String id) throws Exception {
        return service.getLocation(id);
    }

    @Operation(summary = "Get all Academic Levels From External System", tags = {"Academic Level"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = ACADEMIC_LEVELS)
    public IntegrationResponse<List<MCAcademicLevel>> getAcademicLevels() throws Exception {
        return service.getAcademicLevels();
    }

    @Operation(summary = "Get Academic Level From External System", tags = {"Academic Level"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_ACADEMIC_LEVEL)
    public IntegrationResponse<MCAcademicLevel> getAcademicLevel(@PathVariable String id) throws Exception {
        return service.getAcademicLevel(id);
    }

    @Operation(summary = "Get all Rooms From External System", tags = {"Room"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = ROOMS)
    public IntegrationResponse<List<MCRoom>> getRooms() throws Exception {
        return service.getRooms();
    }

    @Operation(summary = "Get Room From External System", tags = {"Room"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = GET_ROOM)
    public IntegrationResponse<MCRoom> getRoom(@PathVariable String id) throws Exception {
        return service.getRoom(id);
    }

    @Operation(summary = "Get Academic Period From External System", tags = {"Academic Period"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Academic periods were found"),
            @ApiResponse(responseCode = "400", description = "Date format on query parameters is incorrect.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = ACADEMIC_PERIODS)
    public IntegrationResponse<List<MCAcademicPeriod>> getAcademicPeriods(
            @ParameterObject @Valid BasePaginationRequest paginationRequest,
            @ParameterObject @Valid SearchMCAcademicPeriodRequest periodRequest)
            throws Exception {
        return service.getAcademicPeriods(paginationRequest, periodRequest);
    }

    @Operation(summary = "Get Academic Period By Id From External System", tags = {"Academic Period"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Academic period was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = GET_ACADEMIC_PERIODS)
    public IntegrationResponse<List<MCAcademicPeriod>> getAcademicPeriods(@PathVariable String id) throws Exception {
        return service.getAcademicPeriod(id);
    }

    @Operation(summary = "Get Instructors From External System", tags = {"Instructor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Instructor was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @GetMapping(path = INSTRUCTORS)
    public IntegrationResponse<List<MCInstructor>> getInstructors(
            @ParameterObject BasePaginationRequest paginationRequest)
            throws Exception {
        return service.getInstructors(paginationRequest);
    }

    @Operation(summary = "Get Instructor From External System", tags = {"Instructor"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Instructor was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = GET_INSTRUCTOR)
    public IntegrationResponse<MCInstructor> getInstructor(@PathVariable String id)
            throws Exception {
        return service.getInstructor(id);
    }

    @Operation(summary = "Get Address From External System By ID", tags = {"Address"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Address was found"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @GetMapping(path = GET_ADDRESS)
    public IntegrationResponse<MCAddress> getAddress(@PathVariable String id)
            throws Exception {
        return service.getAddress(id);
    }

    @Operation(summary = "Health Check to Verify Connectivity To External System", tags = {"Aux"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Connectivity established."),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500")
    })
    @GetMapping(path = HEALTH_CHECK)
    public IntegrationResponse<MCHealthCheck> healthCheck() throws Exception {
        return service.healthCheck();
    }

    @Operation(summary = "Create Final Grade In External System", tags = {"Final Grade"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Final Grade created in External System"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})})
    @PostMapping(path = FINAL_GRADES)
    public IntegrationResponse<CreateMCFinalGradeResponse> createFinalGrades(
            @RequestBody IntegrationRequest<CreateMCFinalGradeRequest> request) throws Exception {
        return service.createFinalGrade(request.getData());
    }

    @Operation(summary = "Update Final Grade In External System", tags = {"Final Grade"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Final Grade was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PutMapping(path = PUT_FINAL_GRADE)
    public IntegrationResponse<ObjectNode> putFinalGrade(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCFinalGradeRequest> updateRequest) throws Exception {
        return service.updateFinalGrade(id, updateRequest.getData());
    }

    @Operation(summary = "Create Organizations In External System", tags = {"Organization"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Organization was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PostMapping(path = ORGANIZATIONS)
    public IntegrationResponse<CreateMCOrganizationResponse> postOrganizations(
            @RequestBody IntegrationRequest<CreateMCOrganizationRequest> createRequest) throws Exception {
        return service.createOrganization( createRequest.getData());
    }

    @Operation(summary = "Update Organizations In External System", tags = {"Organization"})
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Organization was updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))}),
            @ApiResponse(responseCode = "500", description = "Execution Error.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = APIErrorResponseWrapper.class))})
    })
    @PatchMapping(path = UPDATE_ORGANIZATION)
    public IntegrationResponse<ObjectNode> patchOrganizations(
            @PathVariable String id,
            @RequestBody IntegrationRequest<UpdateMCOrganizationRequest> organization)
            throws Exception {
        return service.updateOrganization(id, organization.getData());
    }

}
