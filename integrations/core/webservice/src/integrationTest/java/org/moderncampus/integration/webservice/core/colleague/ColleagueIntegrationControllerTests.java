package org.moderncampus.integration.webservice.core.colleague;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class ColleagueIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${colleague.bearer.token}")
    private String COLLEAGUE_BEARER_TOKEN;

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("colleague.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("colleague.cloud.integration.auth.useHttp", () -> true);
        registry.add("colleague.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(
            @Value("classpath:colleague/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:colleague/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetInsMethodsResp,
            @Value("classpath:colleague/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:colleague/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:colleague/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactTypesResp.json") Resource ethosGetEmergencyContactTypes,
            @Value("classpath:colleague/ethos/EthosGetEmergencyContactPhoneResp.json") Resource ethosGetEmergencyContactPhoneAvailabilities,
            @Value("classpath:colleague/ethos/EthosGetPhoneTypesResp.json") Resource ethosGetPhoneTypes,
            @Value("classpath:colleague/ethos/EthosGetAddressTypesResp.json") Resource ethosGetAddressTypes,
            @Value("classpath:colleague/ethos/EthosGetEmailTypesResp.json") Resource ethosGetEmailTypes,
            @Value("classpath:colleague/ethos/EthosGetPersonNameTypesResp.json") Resource ethosGetPersonNameTypes,
            @Value("classpath:colleague/ethos/EthosGetSectionRegistrationStatusesResp.json") Resource ethosGetSectionRegistrationStatuses,
            @Value("classpath:colleague/ethos/EthosGetCitizenshipStatusesResp.json") Resource ethosGetCitizenshipStatuses,
            @Value("classpath:colleague/ethos/EthosGeSectionGradeTypesResp.json") Resource ethosSectionGradeTypes
            )
            throws Exception {

        String strippedToken = COLLEAGUE_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetInsMethodsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-types", ethosGetEmergencyContactTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/emergency-contact-phone-availabilities",
                ethosGetEmergencyContactPhoneAvailabilities));
        wireMockServer.stubFor(makeRequest(GET, ".*/phone-types", ethosGetPhoneTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/address-types", ethosGetAddressTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/email-types", ethosGetEmailTypes));
        wireMockServer.stubFor(makeRequest(GET, ".*/person-name-types", ethosGetPersonNameTypes));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/section-registration-statuses", ethosGetSectionRegistrationStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/citizenship-statuses", ethosGetCitizenshipStatuses));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-grade-types", ethosSectionGradeTypes));

    }



    @Test
    void colleagueGetAddress(
            @Value("classpath:colleague/ethos/EthosGetAddressByIdResp.json") Resource ethosGetAddressResp,
            @Value("classpath:colleague/GetAddressByIdResp.json") Resource getAddressResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(GET, ".*/addresses/394ca14e-2270-4ecb-b679-504008aeb7d7", ethosGetAddressResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/uapi/integration/v1/addresses/394ca14e-2270-4ecb-b679-504008aeb7d7")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN)
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getAddressResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueGetAcademicLevels(
            @Value("classpath:colleague/ethos/EthosGetAcademicLevelsResp.json") Resource ethosGetAcademicLevelsResp,
            @Value("classpath:colleague/GetAcademicLevelsResp.json") Resource getAcademicLevelsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/academic-levels", ethosGetAcademicLevelsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v6+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/academic-levels")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getAcademicLevelsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueGetSubjects(
            @Value("classpath:colleague/ethos/EthosGetSubjectsResp.json") Resource ethosGetSubjectsResp,
            @Value("classpath:colleague/GetSubjectsResp.json") Resource getSubjectsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/subjects", ethosGetSubjectsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v6+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/subjects")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSubjectsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueGetRooms(
            @Value("classpath:colleague/ethos/EthosGetRoomsResp.json") Resource ethosGetRoomsResp,
            @Value("classpath:colleague/GetRoomsResp.json") Resource getRoomsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/rooms", ethosGetRoomsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v6+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/rooms")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getRoomsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleagueGetLocations(
            @Value("classpath:colleague/ethos/EthosGetLocationsResp.json") Resource ethosGetLocationsResp,
            @Value("classpath:colleague/GetLocationsResp.json") Resource getLocationsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/sites", ethosGetLocationsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v6+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/locations")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getLocationsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleaguePostFinalGrades(
            @Value("classpath:colleague/ethos/EthosPostFinalGradesResp.json") Resource ethosFinalGradesResp,
            @Value("classpath:colleague/ethos/EthosPostFinalGradesReq.json") Resource ethosFinalGradesReq,
            @Value("classpath:colleague/CreateFinalGradesReq.json") Resource createFinalGradesReq,
            @Value("classpath:colleague/CreateFinalGradesResp.json") Resource createFinalGradesResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/student-unverified-grades", ethosFinalGradesResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/final-grades")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(createFinalGradesReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createFinalGradesResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/student-unverified-grades")).withRequestBody(
                equalToJson(asString(ethosFinalGradesReq))));
    }

    @Test
    void colleaguePutFinalGrades(
            @Value("classpath:colleague/ethos/EthosPutFinalGradesResp.json") Resource ethosPutFinalGradesResp,
            @Value("classpath:colleague/ethos/EthosPutFinalGradesReq.json") Resource ethosPutFinalGradesReq,
            @Value("classpath:colleague/UpdateFinalGradesReq.json") Resource updateFinalGradesReq,
            @Value("classpath:colleague/UpdateFinalGradesResp.json") Resource updateFinalGradesResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(PUT, ".*/student-unverified-grades/9b6ea90b-7ade-48b9-87bb-a6c5e097563c", ethosPutFinalGradesResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/final-grades/9b6ea90b-7ade-48b9-87bb-a6c5e097563c")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN).content(asString(updateFinalGradesReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateFinalGradesResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(WireMock.urlMatching(".*/student-unverified-grades/9b6ea90b-7ade-48b9-87bb-a6c5e097563c")).withRequestBody(
                equalToJson(asString(ethosPutFinalGradesReq))));
    }

    @Test
    void colleagueGetInstructionalMethods(
            @Value("classpath:colleague/ethos/EthosGetInstructionalMethodResp.json") Resource ethosGetInstructionalMethods,
            @Value("classpath:colleague/GetInstructionalMethodResp.json") Resource getInstructionalMethodResp
    ) throws Exception {
        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-methods", ethosGetInstructionalMethods));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/uapi/integration/v1/instructional-methods")
                        .contentType("application/json")
                        .header(AUTHORIZATION, COLLEAGUE_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getInstructionalMethodResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

}
