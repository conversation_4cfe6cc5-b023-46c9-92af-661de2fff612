package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCOrganization
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_ORGANIZATION
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCOrganizationToEthosColleagueWriteTransform extends BaseTransformer<MCOrganization, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Autowired
    MCCommonColleagueWriteTransform commonColleagueWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCOrganization orgUpdateRequest) throws Exception {
        def addressTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.ADDRESS_TYPES)
        def phonesTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.PHONE_TYPES)
        def emailTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.EMAIL_TYPES)

        Map<String, ?> ethosRequest = [:]

        def isUpdate = USE_CASE_UPDATE_ORGANIZATION == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)
        if (isUpdate) {
            ethosRequest = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            commonWriteTransform.mapId(ethosRequest)
        }

        if (isValueExist(orgUpdateRequest?.name?.title)) {
            ethosRequest.title = orgUpdateRequest.name.title
        }

        commonColleagueWriteTransform.mapRoles(isUpdate, ethosRequest, orgUpdateRequest.roles)
        commonColleagueWriteTransform.mapCredentials(isUpdate, ethosRequest, orgUpdateRequest.credentials)
        commonColleagueWriteTransform.mapPhones(isUpdate, ethosRequest, orgUpdateRequest.phones, phonesTypesById)
        commonColleagueWriteTransform.mapEmails(isUpdate, ethosRequest, orgUpdateRequest.emails, emailTypesById)

        def updateRequestAddrIds = orgUpdateRequest.addresses*.id.findAll { it != null }
        def existingAddrIds = ethosRequest.addresses?.collect { Map<String, Map<String, String>> it -> it.address?.id } ?: []
        def shouldMapAddresses = shouldUpdateAddresses(updateRequestAddrIds, existingAddrIds, isUpdate)

        if (shouldMapAddresses) {
            commonColleagueWriteTransform.mapAddresses(ctx, isUpdate, ethosRequest, orgUpdateRequest.addresses, addressTypesById)
        }

        return JsonOutput.toJson(ethosRequest)
    }

    def shouldUpdateAddresses(List<String> updateRequestAddrIds, List<String> existingAddrIds, boolean isUpdate) {

        // When creating organizations, we always want to create/update addresses
        if (!isUpdate)
            return true

        //For each Addresses without a GUID MW will include this in the PUT ORGANIZATION
        if (updateRequestAddrIds.isEmpty())
            return true

        //if provided GUID matches one in GET Organization address list = PUT
        //if provided GUID not matching one in GET Person list = Invalid use case
        return updateRequestAddrIds.every { it in existingAddrIds }
    }

}
