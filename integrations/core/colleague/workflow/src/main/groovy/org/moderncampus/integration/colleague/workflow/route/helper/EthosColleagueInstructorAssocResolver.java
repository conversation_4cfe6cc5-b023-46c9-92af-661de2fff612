package org.moderncampus.integration.colleague.workflow.route.helper;

import static org.moderncampus.integration.ellucian.workflow.Constants.ETHOS_ASSOC_INSTRUCTOR;

import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.colleague.workflow.transform.instructor.EthosColleagueInstructorSearchTransforms;
import org.moderncampus.integration.dto.base.SearchEntityRequest;
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.springframework.stereotype.Component;

import groovy.json.JsonSlurper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueInstructorAssocResolver extends ColleagueEthosAssocResolver implements Processor {

    EthosColleagueInstructorSearchTransforms instructorSearchTransforms;

    public EthosColleagueInstructorAssocResolver(CamelContext camelContext, ProducerTemplate producerTemplate,
            EllucianEthosReadEndpointPaginator readEndpointPaginator,
            EthosColleagueInstructorSearchTransforms instructorSearchTransforms) {
        super(camelContext, producerTemplate, readEndpointPaginator);
        this.instructorSearchTransforms = instructorSearchTransforms;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Exchange newExchange = exchange.copy();
        String personId = extractPersonIdFromEthosPersonResp(newExchange);
        if (personId != null) {
            buildInstructorSearchCriteria(personId, newExchange);
            List<Map<String, ?>> parsedAssocResponse = invokeEthosReadEndpoint(ColleagueEthosAPIResource.INSTRUCTORS,
                    newExchange);
            if (!parsedAssocResponse.isEmpty()) {
                exchange.setProperty(ETHOS_ASSOC_INSTRUCTOR, parsedAssocResponse);
            }
        }
    }

    private void buildInstructorSearchCriteria(String personId, Exchange newExchange) {
        SearchEntityRequest entityRequest = new SearchEntityRequest();
        entityRequest.setCriteria(Map.of("instructor", personId));
        newExchange.getMessage().setBody(entityRequest);
        instructorSearchTransforms.buildSearchCriteria(newExchange);
    }

    private String extractPersonIdFromEthosPersonResp(Exchange newExchange) {
        String body = newExchange.getMessage().getBody(String.class);
        Map<String, ?> parsedBody = (Map<String, ?>) new JsonSlurper().parseText(body);
        return (String) parsedBody.get("id");
    }
}
