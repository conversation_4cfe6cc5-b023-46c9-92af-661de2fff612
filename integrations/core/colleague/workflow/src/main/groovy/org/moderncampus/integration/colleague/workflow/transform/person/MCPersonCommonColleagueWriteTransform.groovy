package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCPerson
import org.moderncampus.integration.dto.core.MCPerson.MCPersonAlternativeCredential
import org.moderncampus.integration.dto.core.MCPerson.MCPersonCredential
import org.moderncampus.integration.dto.core.MCPerson.MCPersonEmail
import org.moderncampus.integration.dto.core.MCPerson.MCPersonName
import org.moderncampus.integration.dto.core.MCPerson.MCPersonRole
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.component.internal.IEllucianCloudAPIResource
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.helper.GroovyJsonSupport
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCPersonCommonColleagueWriteTransform {

    void mapAlternativeCredentials(boolean isUseCaseUpdate, Map<String, Object> request, MCPerson person) {
        if (!isValueExist(person.alternativeCredentials)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.alternativeCredentials, request, 'alternativeCredentials',
                (request.alternativeCredentials as List<Map<String, ?>>)?.groupBy { cred -> return cred['type']['id'] as String },
                'createOrUpdateAlternativeCredentials', 'createOrUpdateAlternativeCredentials', null, (personAltCreds) -> personAltCreds.type)

    }

    Map createOrUpdateAlternativeCredentials(MCPersonAlternativeCredential credential, boolean isUpdate, Map<String, ?> existingCred, Object args =
     null) {
        def altCredMap = [:]

        if (isUpdate && existingCred != null) {
            altCredMap = existingCred
        }
        altCredMap.type = [id: credential.type]
        if (isValueExist(credential.value)) {
            altCredMap.value = credential.value
        }
        return altCredMap
    }

    void mapEmails(boolean isUseCaseUpdate, Map<String, Object> request, MCPerson person, Map<String, Map> emailTypesById) {
        if (!isValueExist(person.emails)) return

        boolean isPreferredExisting = false
        if (isUseCaseUpdate) {
            isPreferredExisting = request.emails?.findIndexOf {
                email -> 'primary' == email['preference']
            } >= 0
        }
        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.emails, request, 'emails', (request.emails as
        List<Map<String, ?>>)?.groupBy { name -> return name['type']['detail']['id'] as String }, 'createOrUpdatePersonEmail',
                'createOrUpdatePersonEmail', [emailTypesById, isPreferredExisting], (personEmail) -> {
            if (isValueExist(personEmail.type.id)) {
                return personEmail.type.id
            } else if (isValueExist(personEmail.type.category)) {
                Map.Entry<String, Map> entry = emailTypesById.find { it.value['emailType'] == personEmail.type.category }
                return entry?.key
            }
            return null
        })
    }

    Map createOrUpdatePersonEmail(MCPersonEmail personEmail, boolean isUpdate, Map<String, ?> existingPersonEmail, Map<String, Map> emailTypesById,
     boolean setPrimaryOnUpdate) {
        def emailMap = [:]

        if (isUpdate && existingPersonEmail != null) {
            emailMap = existingPersonEmail
        }
        if (isValueExist(personEmail.address)) emailMap.address = personEmail.address
        if (isValueExist(personEmail.preferred) && ((existingPersonEmail != null && setPrimaryOnUpdate) || !existingPersonEmail)) {
            emailMap.preference = "primary"
        }

        def emailTypeMap = mapTypeWithGivenField(personEmail.type, emailTypesById, "emailType")
        if (emailTypeMap) {
            emailMap.type = emailTypeMap
        }

        return emailMap
    }

    void mapNames(boolean isUseCaseUpdate, Map<String, Object> request, MCPerson person, Map<String, Map> personNameTypesById) {
        if (!isValueExist(person.names)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.names, request, 'names', (request.names as
        List<Map<String, ?>>)?.groupBy { name -> return name['type']['detail']['id'] as String }, "createOrUpdatePersonName",
                "createOrUpdatePersonName", [personNameTypesById], (personName) -> {
            if (isValueExist(personName.type.id)) {
                return personName.type.id
            } else if (isValueExist(personName.type.category)) {
                Map.Entry<String, Map> entry = personNameTypesById.find { it.value['category'] == personName.type.category }
                return entry?.key
            }
            return null
        })
    }

    Map createOrUpdatePersonName(MCPersonName personName, boolean isUpdate, Map<String, ?> existingPersonName, Map<String, Map> personNameTypesById) {
        def nameMap = [:]

        if (isUpdate && existingPersonName != null) {
            nameMap = existingPersonName
        }
        if (isValueExist(personName.title)) nameMap.title = personName.title
        if (isValueExist(personName.firstName)) nameMap.firstName = personName.firstName
        if (isValueExist(personName.middleName)) nameMap.middleName = personName.middleName
        if (isValueExist(personName.lastName)) nameMap.lastName = personName.lastName
        if (isValueExist(personName.suffix)) nameMap.pedigree = personName.suffix

        def fullName = personName.fullName
        if (!isValueExist(fullName)) {
            fullName = "${personName.title ?: ''} ${personName.firstName ?: ''} ${personName.middleName ?: ''} ${personName.lastName ?: ''} " +
                    "${personName.suffix ?: ''}"
        }
        nameMap.fullName = fullName

        def nameTypeMap = mapTypeWithGivenField(personName.type, personNameTypesById, "category")
        if (nameTypeMap) {
            nameMap.type = nameTypeMap
        }

        return nameMap
    }

    void mapCredentials(boolean isUseCaseUpdate, Map<String, ?> request, MCPerson person) {
        if (!isValueExist(person.credentials)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.credentials, request, 'credentials',
                (request.credentials as List<Map<String, ?>>)?.groupBy { cred -> return cred['type'] as String },
                'createOrUpdatePersonCredential', 'createOrUpdatePersonCredential', null, (personCred) -> personCred.type)
    }

    Map createOrUpdatePersonCredential(MCPersonCredential personCred, boolean isUpdate, Map<String, ?> existingPersonCredential, Object args = null) {
        def credMap = [:]

        if (isUpdate && existingPersonCredential != null) {
            credMap = existingPersonCredential
        }

        credMap.type = personCred.type
        credMap.value = personCred.value

        if (isValueExist(personCred.effStart)) {
            credMap.startOn = ISO_LOCAL_DATE.format(personCred.effStart)
        }

        if (isValueExist(personCred.effEnd)) {
            credMap.endOn = ISO_LOCAL_DATE.format(personCred.effEnd)
        }

        return credMap
    }


    void mapRoles(boolean isUseCaseUpdate, Map<String, ?> request, MCPerson person) {
        if (!isValueExist(person.roles)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.roles, request, 'roles', (request.roles as
        List<Map<String, ?>>)?.groupBy { role ->
            return role['role'] as String
        }, 'createOrUpdatePersonRole', 'createOrUpdatePersonRole', null, (role) -> role.role)
    }

    Map createOrUpdatePersonRole(MCPersonRole personRole, boolean isUpdate, Map<String, ?> existingPersonRole, Object args = null) {
        def roleMap = [:]

        if (isUpdate && existingPersonRole != null) {
            roleMap = existingPersonRole
        }

        if (isValueExist(personRole.role)) roleMap.role = personRole.role
        if (isValueExist(personRole.effStart)) roleMap.startOn = ISO_LOCAL_DATE.format(personRole.effStart)
        if (isValueExist(personRole.effEnd)) roleMap.endOn = ISO_LOCAL_DATE.format(personRole.effEnd)

        return roleMap
    }

    Map<String, Map> resourceMapFromContext(TransformContext ctx, ColleagueEthosAPIResource resource) {
        Map<IEllucianCloudAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        return ethosAssocCacheMap[resource] as Map<String, Map>
    }

    Map<String, Object> mapTypeWithGivenField(MCPerson.MCPersonCommonType type, Map<String, Map> typesById, String fieldName) {

        // Only guid was provided, need to search for value
        if (isValueExist(type?.id) && !isValueExist(type?.category)) {
            def typeIdEntry = typesById.find { it.value['id'] == type.id }
            if (typeIdEntry) {
                return [(fieldName): typeIdEntry.value[(fieldName)], detail: [id: typeIdEntry.key]] as Map<String, Object>
            }
        }

        // Just category is passed
        if (isValueExist(type?.category) && !isValueExist(type?.id)) {
            return [(fieldName): type.category] as Map<String, Object>
        }

        // Both values provided, used them
        if (isValueExist(type?.id) && isValueExist(type?.category)) {
            return [(fieldName): type.category, detail: [id: type.id]] as Map<String, Object>
        }

        //No values, do not add type
    }

    void setNestedValue(Map map, Object value, String... path) {
        if (!path) return

        def current = map
        def lastIndex = path.length - 1

        // Create nested structure
        for (int i = 0; i < lastIndex; i++) {
            current.putAt(path[i], current.get(path[i]) ?: [:])
            current = current.get(path[i])
        }

        // Set the final value
        current.putAt(path[lastIndex], value)
    }

}
