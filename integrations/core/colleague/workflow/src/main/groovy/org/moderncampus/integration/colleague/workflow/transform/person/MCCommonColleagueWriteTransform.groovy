package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCAddress
import org.moderncampus.integration.dto.core.MCCategoryType
import org.moderncampus.integration.dto.core.MCEmail
import org.moderncampus.integration.dto.core.MCPerson
import org.moderncampus.integration.dto.core.MCPerson.MCPersonAlternativeCredential
import org.moderncampus.integration.dto.core.MCPerson.MCPersonName
import org.moderncampus.integration.dto.core.MCPhone
import org.moderncampus.integration.dto.core.MCRole
import org.moderncampus.integration.dto.core.MCRoleCredential
import org.moderncampus.integration.helper.GroovyJsonSupport
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.format.DateTimeFormatter

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCCommonColleagueWriteTransform {

    @Autowired
    MCPersonAddressToEthosColleaguePersonAddressWriteTransform personAddressToEthosColleaguePersonAddressWriteTransform

    void mapAlternativeCredentials(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, MCPerson person) {
        if (!isValueExist(person.alternativeCredentials)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.alternativeCredentials, ethosRequest,
        'alternativeCredentials',
                (ethosRequest.alternativeCredentials as List<Map<String, ?>>)?.groupBy { cred -> return cred['type']['id'] as String },
                'createOrUpdateAlternativeCredentials', 'createOrUpdateAlternativeCredentials', null, (personAltCreds) -> personAltCreds.type)

    }

    Map createOrUpdateAlternativeCredentials(MCPersonAlternativeCredential credential, boolean isUpdate, Map<String, ?> existingCred, Object args =
     null) {
        def altCredMap = [:]

        if (isUpdate && existingCred != null) {
            altCredMap = existingCred
        }
        altCredMap.type = [id: credential.type]
        if (isValueExist(credential.value)) {
            altCredMap.value = credential.value
        }
        return altCredMap
    }

    void mapEmails(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, List<? extends MCEmail> emails, Map<String, Map> emailTypesById) {
        if (!isValueExist(emails)) return

        boolean isPreferredExisting = false
        if (isUseCaseUpdate) {
            isPreferredExisting = ethosRequest.emails?.findIndexOf {
                email -> 'primary' == email['preference']
            } >= 0
        }
        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, emails, ethosRequest, 'emails', (ethosRequest.emails as
        List<Map<String, ?>>)?.groupBy { name -> return name['type']['detail']['id'] as String }, 'createOrUpdatePersonEmail',
                'createOrUpdatePersonEmail', [emailTypesById, !isPreferredExisting], (personEmail) -> {
            if (isValueExist(personEmail.type.id)) {
                return personEmail.type.id
            } else if (isValueExist(personEmail.type.category)) {
                Map.Entry<String, Map> entry = emailTypesById.find { it.value['emailType'] == personEmail.type.category }
                return entry?.key
            }
            return null
        })
    }

    void mapGender(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, MCPerson person) {
        if (isValueExist(person.gender)) {
            ethosRequest.put("gender", person.gender)
        }
    }

    void mapDateOfBirth(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, MCPerson person) {
        if (isValueExist(person.dateOfBirth)) {
            ethosRequest.put("dateOfBirth", DateTimeFormatter.ISO_LOCAL_DATE.format(person.dateOfBirth))
        }
    }

    Map createOrUpdatePersonEmail(MCEmail personEmail, boolean isUpdate, Map<String, ?> existingPersonEmail, Map<String, Map> emailTypesById,
                                  boolean setPrimaryOnUpdate) {
        def emailMap = [:]

        if (isUpdate && existingPersonEmail != null) {
            emailMap = existingPersonEmail
        }
        if (isValueExist(personEmail.address)) emailMap.address = personEmail.address
        if (isValueExist(personEmail.preferred) && ((existingPersonEmail != null && setPrimaryOnUpdate) || !existingPersonEmail)) {
            emailMap.preference = "primary"
        }

        def emailTypeMap = mapTypeWithGivenField(personEmail.type, emailTypesById, "emailType")
        if (emailTypeMap) {
            emailMap.type = emailTypeMap
        }

        return emailMap
    }

    void mapNames(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, MCPerson person, Map<String, Map> personNameTypesById) {
        if (!isValueExist(person.names)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.names, ethosRequest, 'names', (ethosRequest.names as
        List<Map<String, ?>>)?.groupBy { name -> return name['type']['detail']['id'] as String }, "createOrUpdatePersonName",
                "createOrUpdatePersonName", [personNameTypesById], (personName) -> {
            if (isValueExist(personName.type.id)) {
                return personName.type.id
            } else if (isValueExist(personName.type.category)) {
                Map.Entry<String, Map> entry = personNameTypesById.find { it.value['category'] == personName.type.category }
                return entry?.key
            }
            return null
        })
    }

    Map createOrUpdatePersonName(MCPersonName personName, boolean isUpdate, Map<String, ?> existingPersonName, Map<String, Map> personNameTypesById) {
        def nameMap = [:]

        if (isUpdate && existingPersonName != null) {
            nameMap = existingPersonName
        }
        if (isValueExist(personName.title)) nameMap.title = personName.title
        if (isValueExist(personName.firstName)) nameMap.firstName = personName.firstName
        if (isValueExist(personName.middleName)) nameMap.middleName = personName.middleName
        if (isValueExist(personName.lastName)) nameMap.lastName = personName.lastName
        if (isValueExist(personName.suffix)) nameMap.pedigree = personName.suffix

        def fullName = personName.fullName
        if (!isValueExist(fullName)) {
            fullName = "${personName.title ?: ''} ${personName.firstName ?: ''} ${personName.middleName ?: ''} ${personName.lastName ?: ''} " +
                    "${personName.suffix ?: ''}"
        }
        nameMap.fullName = fullName

        def nameTypeMap = mapTypeWithGivenField(personName.type, personNameTypesById, "category")
        if (nameTypeMap) {
            nameMap.type = nameTypeMap
        }

        return nameMap
    }

    void mapCredentials(boolean isUseCaseUpdate, Map<String, ?> request, List<? extends MCRoleCredential> credentials) {
        if (!isValueExist(credentials)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, credentials, request, 'credentials',
                (request.credentials as List<Map<String, ?>>)?.groupBy { cred -> return cred['type'] as String },
                'createOrUpdatePersonCredential', 'createOrUpdatePersonCredential', null, (personCred) -> personCred.type)
    }

    Map createOrUpdatePersonCredential(MCRoleCredential personCred, boolean isUpdate, Map<String, ?> existingPersonCredential, Object args = null) {
        def credMap = [:]

        if (isUpdate && existingPersonCredential != null) {
            credMap = existingPersonCredential
        }

        credMap.type = personCred.type
        credMap.value = personCred.value

        if (isValueExist(personCred.effStart)) {
            credMap.startOn = ISO_LOCAL_DATE.format(personCred.effStart)
        }

        if (isValueExist(personCred.effEnd)) {
            credMap.endOn = ISO_LOCAL_DATE.format(personCred.effEnd)
        }

        return credMap
    }


    void mapRoles(boolean isUseCaseUpdate, Map<String, ?> request, List<? extends MCRole> roles) {
        if (!isValueExist(roles)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, roles, request, 'roles', (request.roles as
        List<Map<String, ?>>)?.groupBy { role ->
            return role['role'] as String
        }, 'createOrUpdatePersonRole', 'createOrUpdatePersonRole', null, (role) -> role.role)
    }

    Map createOrUpdatePersonRole(MCRole personRole, boolean isUpdate, Map<String, ?> existingPersonRole, Object args = null) {
        def roleMap = [:]

        if (isUpdate && existingPersonRole != null) {
            roleMap = existingPersonRole
        }

        if (isValueExist(personRole.role)) roleMap.role = personRole.role
        if (isValueExist(personRole.effStart)) roleMap.startOn = ISO_LOCAL_DATE.format(personRole.effStart)
        if (isValueExist(personRole.effEnd)) roleMap.endOn = ISO_LOCAL_DATE.format(personRole.effEnd)

        return roleMap
    }

    Map<String, Object> mapTypeWithGivenField(MCCategoryType type, Map<String, Map> typesById, String fieldName) {

        // Only guid was provided, need to search for value
        if (isValueExist(type?.id) && !isValueExist(type?.category)) {
            def typeIdEntry = typesById.find { it.value['id'] == type.id }
            if (typeIdEntry) {
                return [(fieldName): typeIdEntry.value[(fieldName)], detail: [id: typeIdEntry.key]] as Map<String, Object>
            }
        }

        // Just category is passed
        if (isValueExist(type?.category) && !isValueExist(type?.id)) {
            return [(fieldName): type.category] as Map<String, Object>
        }

        // Both values provided, used them
        if (isValueExist(type?.id) && isValueExist(type?.category)) {
            return [(fieldName): type.category, detail: [id: type.id]] as Map<String, Object>
        }

        //No values, do not add type
    }

    void mapAddresses(TransformContext context, boolean isUseCaseUpdate, Map<String, Object> request,
                      List<? extends MCAddress> addresses, Map<String, Map> addressTypesById) {
        if (!isValueExist(addresses)) return

        boolean isPreferredExisting = false
        if (isUseCaseUpdate) {
            isPreferredExisting = request.addresses?.findIndexOf {
                address -> 'primary' == address['preference']
            } >= 0
        }

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, addresses, request, 'addresses', (request.addresses as
                List<Map<String, ?>>)?.groupBy { address -> return address['address']['id'] as String }, 'createOrUpdatePersonAddress',
                'createOrUpdatePersonAddress',
                [addressTypesById, isPreferredExisting, context], (mcPersonAddress) -> mcPersonAddress.id)
    }

    void mapPhones(boolean isUseCaseUpdate, Map<String, Object> request, List<? extends MCPhone> phones,
                   Map<String, Map> phonesTypesById) {
        if (!isValueExist(phones)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, phones, request, 'phones', (request.phones as
                List<Map<String, ?>>)?.groupBy { phone -> return phone['type']['detail']['id'] as String }, 'createOrUpdatePersonPhone',
                'createOrUpdatePersonPhone', [phonesTypesById],
                (personPhone) -> {
                    if (isValueExist(personPhone.type.id)) {
                        return personPhone.type.id
                    } else if (isValueExist(personPhone.type.category)) {
                        Map.Entry<String, Map> entry = phonesTypesById.find { it.value['phoneType'] == personPhone.type.category }
                        return entry?.key
                    }
                    return null
                })
    }

    Map createOrUpdatePersonPhone(MCPhone personPhone, boolean isUpdate, Map<String, ?> existingPersonPhone,
                                  Map<String, Map> phoneTypesById) {
        def phoneMap = [:]

        if (isUpdate && existingPersonPhone != null) {
            phoneMap = existingPersonPhone
        }
        if (isValueExist(personPhone.number)) {
            phoneMap.number = personPhone.number
        }

        if (isValueExist(personPhone.extension)) {
            phoneMap.extension = personPhone.extension
        }

        def phoneTypeMap = mapTypeWithGivenField(personPhone.type, phoneTypesById, "phoneType")
        if (phoneTypeMap) {
            phoneMap.type = phoneTypeMap
        }

        return phoneMap
    }

    Map createOrUpdatePersonAddress(MCAddress personAddr, boolean isUpdate, Map<String, ?> existingPersonAddress, Map<String,
            Map> addressTypesById, boolean setPrimaryOnUpdate, TransformContext context) {
        def addrMap = [:]

        if (isUpdate && existingPersonAddress != null) {
            return addrMap
        }

        if (isValueExist(personAddr.effStart)) addrMap.startOn = ISO_LOCAL_DATE.format(personAddr.effStart)
        if (isValueExist(personAddr.effEnd)) addrMap.endOn = ISO_LOCAL_DATE.format(personAddr.effEnd)
        if (isValueExist(personAddr.preferred) && ((existingPersonAddress != null && setPrimaryOnUpdate) || !existingPersonAddress)) {
            addrMap.preference = "primary"
        }

        def addrTypeMap = mapTypeWithGivenField(personAddr.type, addressTypesById, "addressType")
        if (addrTypeMap) {
            addrMap.type = addrTypeMap
        }

        addrMap.address = personAddressToEthosColleaguePersonAddressWriteTransform.createOrUpdateAddress(personAddr, existingPersonAddress,
                addressTypesById)
        return addrMap
    }

}
