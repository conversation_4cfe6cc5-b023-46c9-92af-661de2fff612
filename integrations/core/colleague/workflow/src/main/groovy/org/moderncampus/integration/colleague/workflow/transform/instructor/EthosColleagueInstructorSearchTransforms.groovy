package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.colleague.workflow.transform.common.search.BaseEthosColleagueSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueInstructorSearchTransforms extends BaseEthosColleagueSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        // Don't add complex criteria to avoid 405 errors
        // We'll filter the results in the resolver instead
        // This allows the endpoint to return all instructors which we then filter by person ID
    }
}
