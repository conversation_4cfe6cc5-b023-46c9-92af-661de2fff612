package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.colleague.workflow.transform.common.search.BaseEthosColleagueSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueInstructorSearchTransforms extends BaseEthosColleagueSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def instructor = searchCriteria["instructor"]
        if (instructor) {
            queryParams["criteria"] = JsonOutput.toJson(["instructor": ["id": instructor]])
        }
    }
}
