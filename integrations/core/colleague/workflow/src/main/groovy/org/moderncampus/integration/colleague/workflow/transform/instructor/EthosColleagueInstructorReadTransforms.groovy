package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCCombinedInstructor
import org.moderncampus.integration.dto.core.MCInstructor
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueInstructorReadTransforms {

    ITransformer<String, MCCombinedInstructor> mcCombinedInstructorTransformer = { TransformContext ctx, String body ->
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def instructor = new MCCombinedInstructor()

        // Use PERSON ID as primary identifier
        instructor.id = rootNode.id as String

        // Map names from PERSONS data
        mapNames(instructor, rootNode)

        // Map emails from PERSONS data
        mapEmails(instructor, rootNode)

        // Map instructor types and departments from INSTRUCTORS association data
        mapInstructorAssociations(instructor, ctx)

        // Set status - always "A" since we filter by instructor role
        instructor.status = "A"

        return instructor
    }

    private void mapNames(MCCombinedInstructor instructor, Map<String, ?> personData) {
        def names = personData.names as List<Map<String, ?>>
        if (names) {
            instructor.names = names.collect { nameData ->
                def name = new MCCombinedInstructor.MCCombinedInstructorName()
                // Generate a unique ID for the name based on person ID and name type
                def typeId = (nameData.type as Map<String, ?>)?.id ?: 'default'
                name.id = "${instructor.id}_name_${typeId}"

                // Check if this is the preferred name (usually the first one or marked as preferred)
                name.preferred = nameData.preference == "preferred" || names.indexOf(nameData) == 0

                name.title = nameData.title as String
                name.firstName = nameData.firstName as String
                name.middleName = nameData.middleName as String
                name.lastName = nameData.lastName as String
                name.suffix = nameData.pedigree as String

                // Map name type
                if (nameData.type) {
                    def typeData = nameData.type as Map<String, ?>
                    name.type = new MCCombinedInstructor.MCCombinedInstructorNameType()
                    name.type.id = typeData.id as String
                    def detailData = typeData.detail as Map<String, ?>
                    name.type.name = detailData?.id as String ?: "legal"
                }

                return name
            }
        }
    }

    private void mapEmails(MCCombinedInstructor instructor, Map<String, ?> personData) {
        def emailAddresses = personData.emailAddresses as List<Map<String, ?>>
        if (emailAddresses) {
            instructor.emails = emailAddresses.collect { emailData ->
                def email = new MCCombinedInstructor.MCCombinedInstructorEmail()
                // Generate a unique ID for the email based on person ID and email type
                def emailTypeId = (emailData.type as Map<String, ?>)?.id ?: 'default'
                email.id = "${instructor.id}_email_${emailTypeId}"

                // Check if this is the preferred email (usually the first one or marked as preferred)
                email.preferred = emailAddresses.indexOf(emailData) == 0
                email.address = emailData.address as String

                // Map email type
                if (emailData.type) {
                    def typeData = emailData.type as Map<String, ?>
                    email.type = new MCCombinedInstructor.MCCombinedInstructorEmailType()
                    email.type.id = typeData.id as String
                    def emailDetailData = typeData.detail as Map<String, ?>
                    email.type.name = emailDetailData?.id as String ?: typeData.id as String
                }

                return email
            }
        }
    }

    private void mapInstructorAssociations(MCCombinedInstructor instructor, TransformContext ctx) {
        def instructorAssocData = ctx.contextProps.get("ETHOS_ASSOC_INSTRUCTOR") as List<Map<String, ?>>

        if (instructorAssocData) {
            // Extract unique instructor types and departments
            def instructorTypes = [] as Set<Map<String, String>>
            def departments = [] as Set<Map<String, String>>

            instructorAssocData.each { instructorData ->
                // Extract instructor types from the instructor data itself
                // The instructor type can be derived from the instructor record
                if (instructorData.id) {
                    instructorTypes.add([
                        id: instructorData.id as String,
                        name: "instructor" // Default type name
                    ])
                }

                // Extract departments from institutional units
                def institutionalUnits = instructorData.institutionalUnits as List<Map<String, ?>>
                institutionalUnits?.each { unit ->
                    def department = unit.department as Map<String, ?>
                    if (department?.id) {
                        departments.add([
                            id: department.id as String,
                            name: department.title as String ?: department.id as String,
                            code: department.code as String ?: department.id as String
                        ])
                    }
                }
            }

            // Map unique instructor types
            instructor.instructorTypes = instructorTypes.collect { typeData ->
                def type = new MCCombinedInstructor.MCCombinedInstructorType()
                type.id = typeData.id
                return type
            }

            // Map unique instructor departments
            instructor.instructorDepartments = departments.collect { deptData ->
                def dept = new MCCombinedInstructor.MCCombinedInstructorDepartment()
                dept.id = deptData.id
                return dept
            }

            // Map unique departments for Valence (simplified department list)
            instructor.departments = departments.collect { deptData ->
                def dept = new MCCombinedInstructor.MCCombinedDepartment()
                dept.id = deptData.id
                dept.name = deptData.name
                dept.code = deptData.code
                return dept
            }
        } else {
            // If no instructor association data, set default values
            instructor.instructorTypes = []
            instructor.instructorDepartments = []
            instructor.departments = []
        }
    }

    // Transform for individual instructor endpoint (returns MCInstructor)
    ITransformer<String, MCInstructor> mcInstructorTransformer = { TransformContext ctx, String body ->
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def instructor = new MCInstructor()

        // Use PERSON ID as primary identifier
        instructor.id = rootNode.id as String

        // Map names from PERSONS data
        mapNamesForMCInstructor(instructor, rootNode)

        // Map emails from PERSONS data
        mapEmailsForMCInstructor(instructor, rootNode)

        // Map instructor types and departments from INSTRUCTORS association data
        mapInstructorAssociationsForMCInstructor(instructor, ctx)

        // Set status - always "A" since we filter by instructor role
        instructor.status = "A"

        return instructor
    }

    private void mapNamesForMCInstructor(MCInstructor instructor, Map<String, ?> personData) {
        def names = personData.names as List<Map<String, ?>>
        if (names) {
            instructor.names = names.collect { nameData ->
                def name = new MCInstructor.MCInstructorName()
                // Generate a unique ID for the name based on person ID and name type
                def nameTypeId = (nameData.type as Map<String, ?>)?.id ?: 'default'
                name.id = "${instructor.id}_name_${nameTypeId}"

                // Check if this is the preferred name (usually the first one or marked as preferred)
                name.preferred = nameData.preference == "preferred" || names.indexOf(nameData) == 0

                name.title = nameData.title as String
                name.firstName = nameData.firstName as String
                name.middleName = nameData.middleName as String
                name.lastName = nameData.lastName as String
                name.suffix = nameData.pedigree as String

                // Map name type
                if (nameData.type) {
                    def typeData = nameData.type as Map<String, ?>
                    name.type = new MCInstructor.MCInstructorNameType()
                    name.type.id = typeData.id as String
                    def nameDetailData = typeData.detail as Map<String, ?>
                    name.type.name = nameDetailData?.id as String ?: "legal"
                }

                return name
            }
        }
    }

    private void mapEmailsForMCInstructor(MCInstructor instructor, Map<String, ?> personData) {
        def emailAddresses = personData.emailAddresses as List<Map<String, ?>>
        if (emailAddresses) {
            instructor.emails = emailAddresses.collect { emailData ->
                def email = new MCInstructor.MCInstructorEmail()
                // Generate a unique ID for the email based on person ID and email type
                def emailTypeId2 = (emailData.type as Map<String, ?>)?.id ?: 'default'
                email.id = "${instructor.id}_email_${emailTypeId2}"

                // Check if this is the preferred email (usually the first one or marked as preferred)
                email.preferred = emailAddresses.indexOf(emailData) == 0
                email.address = emailData.address as String

                // Map email type
                if (emailData.type) {
                    def typeData = emailData.type as Map<String, ?>
                    email.type = new MCInstructor.MCInstructorEmailType()
                    email.type.id = typeData.id as String
                    def emailDetailData2 = typeData.detail as Map<String, ?>
                    email.type.name = emailDetailData2?.id as String ?: typeData.id as String
                }

                return email
            }
        }
    }

    private void mapInstructorAssociationsForMCInstructor(MCInstructor instructor, TransformContext ctx) {
        def instructorAssocData = ctx.contextProps.get("ETHOS_ASSOC_INSTRUCTOR") as List<Map<String, ?>>

        if (instructorAssocData) {
            // Extract unique instructor types and departments
            def instructorTypes = [] as Set<Map<String, String>>
            def departments = [] as Set<Map<String, String>>

            instructorAssocData.each { instructorData ->
                // Extract instructor types from the instructor data itself
                if (instructorData.id) {
                    instructorTypes.add([
                        id: instructorData.id as String,
                        name: "instructor" // Default type name
                    ])
                }

                // Extract departments from institutional units
                def institutionalUnits = instructorData.institutionalUnits as List<Map<String, ?>>
                institutionalUnits?.each { unit ->
                    def department = unit.department as Map<String, ?>
                    if (department?.id) {
                        departments.add([
                            id: department.id as String,
                            name: department.title as String ?: department.id as String,
                            code: department.code as String ?: department.id as String
                        ])
                    }
                }
            }

            // Map unique instructor types
            instructor.instructorTypes = instructorTypes.collect { typeData ->
                def type = new MCInstructor.MCInstructorType()
                type.id = typeData.id
                return type
            }

            // Map unique instructor departments
            instructor.instructorDepartments = departments.collect { deptData ->
                def dept = new MCInstructor.MCInstructorDepartment()
                dept.id = deptData.id
                return dept
            }

            // Map unique departments for Valence (simplified department list)
            instructor.departments = departments.collect { deptData ->
                def dept = new MCInstructor.MCDepartment()
                dept.id = deptData.id
                dept.name = deptData.name
                dept.code = deptData.code
                return dept
            }
        } else {
            // If no instructor association data, set default values
            instructor.instructorTypes = []
            instructor.instructorDepartments = []
            instructor.departments = []
        }
    }

    ITransformer<String, MCCombinedInstructor> getMcCombinedInstructorTransformer() {
        return mcCombinedInstructorTransformer
    }

    ITransformer<String, MCInstructor> getMcInstructorTransformer() {
        return mcInstructorTransformer
    }
}
