package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.dto.core.MCCombinedInstructor
import org.moderncampus.integration.ellucian.workflow.Constants
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueInstructorReadTransforms extends BaseTransformer {

    ITransformer<String, MCCombinedInstructor> mcCombinedInstructorTransformer = { TransformContext ctx, String body ->
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def instructor = new MCCombinedInstructor()

        // Use PERSON ID as primary identifier
        instructor.id = rootNode.id as String

        // Map names from PERSONS data
        mapNames(instructor, rootNode)

        // Map emails from PERSONS data  
        mapEmails(instructor, rootNode)

        // Map instructor types and departments from INSTRUCTORS association data
        mapInstructorAssociations(instructor, ctx.exchange)

        // Set status - always "A" since we filter by instructor role
        instructor.status = "A"

        return instructor
    }

    private void mapNames(MCCombinedInstructor instructor, Map<String, ?> personData) {
        def names = personData.names as List<Map<String, ?>>
        if (names) {
            instructor.names = names.collect { nameData ->
                def name = new MCCombinedInstructor.MCCombinedInstructorName()
                name.id = nameData.id as String  // Add id field
                name.preferred = "preferred".equals(nameData.preference)
                name.title = nameData.title as String
                name.firstName = nameData.firstName as String
                name.middleName = nameData.middleName as String
                name.lastName = nameData.lastName as String
                name.suffix = nameData.pedigree as String

                // Map name type
                if (nameData.type) {
                    def typeData = nameData.type as Map<String, ?>
                    name.type = new MCCombinedInstructor.MCCombinedInstructorNameType()
                    name.type.id = typeData.detail?.id as String
                    name.type.name = typeData.category as String
                }

                return name
            }
        }
    }

    private void mapEmails(MCCombinedInstructor instructor, Map<String, ?> personData) {
        def emails = personData.emails as List<Map<String, ?>>
        if (emails) {
            instructor.emails = emails.collect { emailData ->
                def email = new MCCombinedInstructor.MCCombinedInstructorEmail()
                email.id = emailData.id as String  // Add id field
                email.preferred = "primary".equals(emailData.preference)
                email.address = emailData.address as String

                // Map email type
                if (emailData.type) {
                    def typeData = emailData.type as Map<String, ?>
                    email.type = new MCCombinedInstructor.MCCombinedInstructorEmailType()
                    email.type.id = typeData.detail?.id as String
                    email.type.name = typeData.emailType as String
                }

                return email
            }
        }
    }

    private void mapInstructorAssociations(MCCombinedInstructor instructor, Exchange exchange) {
        def instructorAssocData = exchange.getProperty(Constants.ETHOS_ASSOC_INSTRUCTOR) as List<Map<String, ?>>
        
        if (instructorAssocData) {
            // Extract unique instructor types
            def instructorTypes = [] as Set<String>
            def departments = [] as Set<String>

            instructorAssocData.each { instructorData ->
                // Extract instructor categories/types
                if (instructorData.category?.id) {
                    instructorTypes.add(instructorData.category.id as String)
                }

                // Extract departments from institutional units
                def institutionalUnits = instructorData.institutionalUnits as List<Map<String, ?>>
                institutionalUnits?.each { unit ->
                    if (unit.department?.id) {
                        departments.add(unit.department.id as String)
                    }
                }
            }

            // Map unique instructor types
            instructor.instructorTypes = instructorTypes.collect { typeId ->
                def type = new MCCombinedInstructor.MCCombinedInstructorType()
                type.id = typeId
                return type
            }

            // Map unique departments
            instructor.instructorDepartments = departments.collect { deptId ->
                def dept = new MCCombinedInstructor.MCCombinedInstructorDepartment()
                dept.id = deptId
                return dept
            }
        }
    }

    ITransformer<String, MCCombinedInstructor> getMcCombinedInstructorTransformer() {
        return mcCombinedInstructorTransformer
    }
}
