import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id "groovy"
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    implementation enforcedPlatform("org.apache.camel.springboot:camel-spring-boot-bom:${libCamel}")

    implementation 'org.springframework:spring-context'
    compileOnly "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"

    implementation project(':common:common-common')
    implementation project(':integrations:common:int-common-core')
    implementation project(':integrations:common:int-common-ellucian')

    implementation 'org.apache.camel.springboot:camel-spring-boot-starter'
    implementation 'org.apache.camel.springboot:camel-console-starter'
    implementation 'org.apache.camel.springboot:camel-stream-starter'
    implementation 'org.apache.camel.springboot:camel-jackson-starter'
    implementation 'org.apache.camel.springboot:camel-bean-validator-starter'
    implementation 'org.apache.camel.springboot:camel-jsonpath-starter'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    implementation 'org.apache.camel.springboot:camel-groovy-starter'
    implementation 'org.apache.groovy:groovy-json'

    implementation "org.apache.commons:commons-lang3"
    implementation "commons-io:commons-io:${libCommonsIo}"

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation "org.springframework:spring-web"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$libJUnitJupiter"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "org.apache.camel:camel-test-spring-junit5:${libCamel}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$libJUnitJupiter"
}

compileGroovy {
    options.encoding = compileJava.options.encoding
    options.compilerArgs.addAll(compileJava.options.compilerArgs)
}

test {
    useJUnitPlatform()
}

// Handle duplicate files in JAR
jar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
