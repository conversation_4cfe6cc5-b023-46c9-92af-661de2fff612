package org.moderncampus.integration.banner.webservice.model.instructor;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Instructor {
    private String id;
    private List<InstructorName> names;
    private List<InstructorEmail> emails;
    private List<InstructorType> instructorTypes;
    private List<Department> departments;
    private String status;
}