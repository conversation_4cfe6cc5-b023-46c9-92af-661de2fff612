package org.moderncampus.integration.banner.webservice.model.instructor;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.moderncampus.integration.webservice.model.PaginationResponse;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InstructorResponse {
    private List<Instructor> data;
    private PaginationResponse pagination;
}