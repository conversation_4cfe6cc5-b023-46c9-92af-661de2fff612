import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

tasks.named("jar") {
    archiveClassifier = ''
    enabled = true
}

// Disable bootJar since this is a library, not an executable application
bootJar {
    enabled = false
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.session:spring-session-core'

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"

    implementation project(':common:common-common')
    implementation project(':common:common-tenants')

    implementation "com.auth0:java-jwt:${libJavaJwt}"

    implementation 'org.apache.commons:commons-lang3'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation 'org.springframework.security:spring-security-test'
}